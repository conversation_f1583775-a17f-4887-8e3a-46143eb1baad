<?php
/**
 * Single template for Scholarship post type
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

while (have_posts()) : the_post();
    // Get custom fields
    $scholarship_name = get_post_meta(get_the_ID(), 'scholarship_name', true);
    $scholarship_value = get_post_meta(get_the_ID(), 'scholarship_value', true);
    $tuition_support = get_post_meta(get_the_ID(), 'tuition_support', true);
    $related_university_id = get_post_meta(get_the_ID(), 'related_university', true);
    
    // Get university info
    $university = null;
    $university_name = '';
    $university_country = '';
    $university_location = '';
    $university_website = '';
    if ($related_university_id) {
        $university = get_post($related_university_id);
        if ($university) {
            $university_name = get_post_meta($related_university_id, 'university_name', true) ?: $university->post_title;
            $university_country = get_post_meta($related_university_id, 'country', true);
            $university_location = get_post_meta($related_university_id, 'location', true);
            $university_website = get_post_meta($related_university_id, 'website_url', true);
        }
    }
    
    // Get country name
    $country_names = array(
        'usa' => 'Hoa Kỳ',
        'uk' => 'Anh',
        'canada' => 'Canada',
        'australia' => 'Úc',
        'germany' => 'Đức',
        'france' => 'Pháp',
        'japan' => 'Nhật Bản',
        'south_korea' => 'Hàn Quốc',
        'singapore' => 'Singapore',
        'netherlands' => 'Hà Lan',
        'other' => 'Khác'
    );
    $country_display = isset($country_names[$university_country]) ? $country_names[$university_country] : $university_country;
?>

<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="relative" style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>'); background-size: cover; background-position: center;">
        <?php if (has_post_thumbnail()) : ?>
            <div class="relative h-96 overflow-hidden">
                <?php the_post_thumbnail('full', array('class' => 'w-full h-full object-cover')); ?>
                <div class="absolute inset-0 bg-gradient-to-r from-[#fa3c80]/80 to-[#004aad]/80"></div>
            </div>
        <?php else : ?>
            <div class="h-96 bg-gradient-to-r from-[#fa3c80] to-[#004aad]"></div>
        <?php endif; ?>
        
        <div class="absolute inset-0 flex items-center">
            <div class="container mx-auto px-4">
                <div class="text-white max-w-4xl">
                    <div class="flex items-center mb-4">
                        <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium mr-4">
                            <?php _e('Học bổng', 'inthub'); ?>
                        </span>
                        <?php if ($country_display) : ?>
                            <span class="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                                <?php echo esc_html($country_display); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                    <h1 class="text-4xl md:text-5xl font-bold mb-4">
                        <?php echo esc_html($scholarship_name ?: get_the_title()); ?>
                    </h1>
                    <?php if ($university_name) : ?>
                        <div class="flex items-center text-xl opacity-90 mb-6">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <span><a href="<?php echo get_permalink($related_university_id); ?>"><?php echo esc_html($university_name); ?></a></span>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($scholarship_value) : ?>
                        <div class="flex items-center text-2xl font-bold mb-6">
                            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <span><?php echo esc_html($scholarship_value); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="flex flex-wrap gap-4">
                        <?php if ($university && $related_university_id) : ?>
                            <a href="<?php echo get_permalink($related_university_id); ?>" 
                               class="inline-flex items-center bg-white text-[#004aad] px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <?php _e('Xem trường đại học', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if ($university_website) : ?>
                            <a href="<?php echo esc_url($university_website); ?>" target="_blank" 
                               class="inline-flex items-center bg-[#fa3c80] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#fa3c80]/90 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                <?php _e('Ứng tuyển ngay', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- Scholarship Details -->
                    <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                        <h2 class="text-2xl font-bold text-[#004aad] mb-6">
                            <?php _e('Chi tiết học bổng', 'inthub'); ?>
                        </h2>
                        
                        <?php if (get_the_content()) : ?>
                            <div class="prose prose-lg max-w-none mb-6">
                                <?php the_content(); ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($tuition_support) : ?>
                            <div class="bg-gradient-to-r from-[#004aad]/10 to-[#fa3c80]/10 rounded-lg p-6 mb-6">
                                <h3 class="text-lg font-semibold text-[#004aad] mb-3">
                                    <?php _e('Hỗ trợ học phí', 'inthub'); ?>
                                </h3>
                                <p class="text-gray-700"><?php echo esc_html($tuition_support); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Application Requirements -->
                        <?php
                        $application_requirements = inthub_get_application_requirements();
                        if ($application_requirements) : ?>
                            <div class="border-t pt-6">
                                <h3 class="text-lg font-semibold text-[#004aad] mb-4">
                                    <?php _e('Yêu cầu ứng tuyển', 'inthub'); ?>
                                </h3>
                                <div class="space-y-4">
                                    <?php foreach ($application_requirements as $index => $requirement) :
                                        $title = isset($requirement['title']) ? $requirement['title'] : '';
                                        $description = isset($requirement['description']) ? $requirement['description'] : '';

                                        if (empty($title) && empty($description)) continue;
                                    ?>
                                        <div class="flex items-start p-4 bg-gray-50 rounded-lg">
                                            <div class="flex-shrink-0 w-6 h-6 bg-[#fa3c80] text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                                                <?php echo $index + 1; ?>
                                            </div>
                                            <div class="flex-1">
                                                <?php if (!empty($title)) : ?>
                                                    <h4 class="font-semibold text-gray-900 mb-2"><?php echo esc_html($title); ?></h4>
                                                <?php endif; ?>
                                                <?php if (!empty($description)) : ?>
                                                    <div class="text-gray-700 leading-relaxed">
                                                        <?php echo wp_kses_post(wpautop($description)); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else : ?>
                            <!-- Fallback to default requirements if no custom requirements are set -->
                            <div class="border-t pt-6">
                                <h3 class="text-lg font-semibold text-[#004aad] mb-4">
                                    <?php _e('Yêu cầu ứng tuyển', 'inthub'); ?>
                                </h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="flex items-start p-4 bg-gray-50 rounded-lg">
                                        <svg class="w-5 h-5 text-[#fa3c80] mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900"><?php _e('Thành tích học tập', 'inthub'); ?></div>
                                            <div class="text-sm text-gray-600"><?php _e('GPA tối thiểu 3.0/4.0', 'inthub'); ?></div>
                                        </div>
                                    </div>
                                    <div class="flex items-start p-4 bg-gray-50 rounded-lg">
                                        <svg class="w-5 h-5 text-[#fa3c80] mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900"><?php _e('Chứng chỉ tiếng Anh', 'inthub'); ?></div>
                                            <div class="text-sm text-gray-600"><?php _e('IELTS 6.5+ hoặc TOEFL 80+', 'inthub'); ?></div>
                                        </div>
                                    </div>
                                    <div class="flex items-start p-4 bg-gray-50 rounded-lg">
                                        <svg class="w-5 h-5 text-[#fa3c80] mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900"><?php _e('Thư giới thiệu', 'inthub'); ?></div>
                                            <div class="text-sm text-gray-600"><?php _e('2-3 thư từ giáo viên/giảng viên', 'inthub'); ?></div>
                                        </div>
                                    </div>
                                    <div class="flex items-start p-4 bg-gray-50 rounded-lg">
                                        <svg class="w-5 h-5 text-[#fa3c80] mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                        <div>
                                            <div class="font-medium text-gray-900"><?php _e('Luận văn động lực', 'inthub'); ?></div>
                                            <div class="text-sm text-gray-600"><?php _e('Personal Statement', 'inthub'); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Application Process -->
                    <div class="bg-white rounded-xl shadow-lg p-8">
                        <h2 class="text-2xl font-bold text-[#004aad] mb-6">
                            <?php _e('Quy trình ứng tuyển', 'inthub'); ?>
                        </h2>

                        <?php
                        $application_process = inthub_get_application_process();
                        if ($application_process) : ?>
                            <div class="space-y-6">
                                <?php foreach ($application_process as $index => $step) :
                                    $step_number = $index + 1;
                                    $title = isset($step['title']) ? $step['title'] : '';
                                    $description = isset($step['description']) ? $step['description'] : '';

                                    if (empty($title) && empty($description)) continue;

                                    // Use different color for last step
                                    $bg_color = ($step_number === count($application_process)) ? 'bg-[#fa3c80]' : 'bg-[#004aad]';
                                ?>
                                    <div class="flex items-start">
                                        <div class="flex-shrink-0 w-8 h-8 bg-[#fa3c80] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">
                                            <?php echo $step_number; ?>
                                        </div>
                                        <div class="flex-1">
                                            <?php if (!empty($title)) : ?>
                                                <h3 class="font-semibold text-gray-900 mb-2"><?php echo esc_html($title); ?></h3>
                                            <?php endif; ?>
                                            <?php if (!empty($description)) : ?>
                                                <div class="text-gray-600 leading-relaxed">
                                                    <?php echo wp_kses_post(wpautop($description)); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php else : ?>
                            <!-- Fallback to default process if no custom process is set -->
                            <div class="space-y-6">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-[#fa3c80] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">1</div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-2"><?php _e('Chuẩn bị hồ sơ', 'inthub'); ?></h3>
                                        <p class="text-gray-600"><?php _e('Thu thập tất cả các tài liệu cần thiết như bảng điểm, chứng chỉ tiếng Anh, thư giới thiệu.', 'inthub'); ?></p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-[#fa3c80] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">2</div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-2"><?php _e('Nộp đơn trực tuyến', 'inthub'); ?></h3>
                                        <p class="text-gray-600"><?php _e('Điền đầy đủ thông tin và tải lên tất cả tài liệu qua hệ thống online của trường.', 'inthub'); ?></p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-[#fa3c80] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">3</div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-2"><?php _e('Phỏng vấn', 'inthub'); ?></h3>
                                        <p class="text-gray-600"><?php _e('Tham gia phỏng vấn trực tuyến hoặc trực tiếp với ban tuyển sinh.', 'inthub'); ?></p>
                                    </div>
                                </div>
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 w-8 h-8 bg-[#fa3c80] text-white rounded-full flex items-center justify-center font-bold text-sm mr-4">4</div>
                                    <div>
                                        <h3 class="font-semibold text-gray-900 mb-2"><?php _e('Nhận kết quả', 'inthub'); ?></h3>
                                        <p class="text-gray-600"><?php _e('Chờ thông báo kết quả và làm thủ tục nhập học nếu được chấp nhận.', 'inthub'); ?></p>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Quick Info -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 sticky top-8">
                        <h3 class="text-xl font-bold text-[#004aad] mb-4">
                            <?php _e('Thông tin học bổng', 'inthub'); ?>
                        </h3>
                        
                        <div class="space-y-4">
                            <?php if ($scholarship_value) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Giá trị', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($scholarship_value); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($university_name) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Trường đại học', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($university_name); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($country_display) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Quốc gia', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($country_display); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($university_location) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Địa điểm', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($university_location); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                        
                        <div class="mt-6 pt-6 border-t space-y-3">
                            <?php if ($university_website) : ?>
                                <a href="<?php echo esc_url($university_website); ?>" target="_blank" 
                                   class="w-full inline-flex items-center justify-center bg-[#004aad] text-white px-4 py-3 rounded-lg font-medium hover:bg-[#004aad]/90 transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    <?php _e('Ứng tuyển ngay', 'inthub'); ?>
                                </a>
                            <?php endif; ?>
                            
                            <?php if ($university && $related_university_id) : ?>
                                <a href="<?php echo get_permalink($related_university_id); ?>" 
                                   class="w-full inline-flex items-center justify-center bg-[#fa3c80] text-white px-4 py-3 rounded-lg font-medium hover:bg-[#fa3c80]/90 transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                    </svg>
                                    <?php _e('Xem trường đại học', 'inthub'); ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<?php endwhile; ?>

<?php get_footer(); ?>
