<?php
/**
 * IntHub Theme Functions
 * 
 * @package IntHub
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Theme Setup
 */
function inthub_setup() {
    // Add theme support for various features
    add_theme_support('post-thumbnails');
    add_theme_support('title-tag');
    add_theme_support('custom-logo');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Add support for responsive embeds
    add_theme_support('responsive-embeds');
    
    // Add support for editor styles
    add_theme_support('editor-styles');
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'inthub'),
        'footer' => __('Footer Menu', 'inthub'),
    ));
    
    // Set content width
    if (!isset($content_width)) {
        $content_width = 1200;
    }
}
add_action('after_setup_theme', 'inthub_setup');

/**
 * Enqueue Scripts and Styles
 */
function inthub_scripts() {
    // Enqueue main stylesheet
    wp_enqueue_style('inthub-style', get_stylesheet_uri(), array(), '1.0.0');
    
    // Enqueue Tailwind CSS from CDN
    wp_enqueue_style('tailwind-css', 'https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css', array(), '2.2.19');
    
    // Enqueue custom CSS
    wp_enqueue_style('inthub-custom', get_template_directory_uri() . '/assets/css/custom.css', array('inthub-style'), '1.0.0');
    
    // Enqueue main JavaScript
    wp_enqueue_script('inthub-main', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);
    
    // Enqueue slider JavaScript
    wp_enqueue_script('inthub-slider', get_template_directory_uri() . '/assets/js/slider.js', array('jquery'), '1.0.0', true);
    
    // Enqueue navigation JavaScript
    wp_enqueue_script('inthub-navigation', get_template_directory_uri() . '/assets/js/navigation.js', array('jquery'), '1.0.0', true);

    // Enqueue University CSS and JS on university pages
    if (is_post_type_archive('university') || is_singular('university')) {
        wp_enqueue_style('inthub-university', get_template_directory_uri() . '/assets/css/university.css', array('inthub-custom'), '1.0.0');
        wp_enqueue_script('inthub-university', get_template_directory_uri() . '/assets/js/university.js', array('jquery'), '1.0.0', true);
    }

    // Enqueue Scholarship CSS and JS on scholarship pages
    if (is_post_type_archive('scholarship') || is_singular('scholarship')) {
        wp_enqueue_style('inthub-scholarship', get_template_directory_uri() . '/assets/css/scholarship.css', array('inthub-custom'), '1.0.0');
        wp_enqueue_script('inthub-scholarship', get_template_directory_uri() . '/assets/js/scholarship.js', array('jquery'), '1.0.0', true);
    }

    // Localize script for AJAX
    wp_localize_script('inthub-main', 'inthub_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('inthub_nonce'),
    ));
}
add_action('wp_enqueue_scripts', 'inthub_scripts');

/**
 * Register Widget Areas
 */
function inthub_widgets_init() {
    register_sidebar(array(
        'name'          => __('Sidebar', 'inthub'),
        'id'            => 'sidebar-1',
        'description'   => __('Add widgets here to appear in your sidebar.', 'inthub'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 1', 'inthub'),
        'id'            => 'footer-1',
        'description'   => __('Add widgets here to appear in the first footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 2', 'inthub'),
        'id'            => 'footer-2',
        'description'   => __('Add widgets here to appear in the second footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 3', 'inthub'),
        'id'            => 'footer-3',
        'description'   => __('Add widgets here to appear in the third footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
    
    register_sidebar(array(
        'name'          => __('Footer 4', 'inthub'),
        'id'            => 'footer-4',
        'description'   => __('Add widgets here to appear in the fourth footer column.', 'inthub'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h4 class="widget-title">',
        'after_title'   => '</h4>',
    ));
}
add_action('widgets_init', 'inthub_widgets_init');

/**
 * Custom Post Types
 */
function inthub_custom_post_types() {
    // Testimonials Post Type
    register_post_type('testimonials', array(
        'labels' => array(
            'name' => __('Testimonials', 'inthub'),
            'singular_name' => __('Testimonial', 'inthub'),
            'add_new' => __('Add New Testimonial', 'inthub'),
            'add_new_item' => __('Add New Testimonial', 'inthub'),
            'edit_item' => __('Edit Testimonial', 'inthub'),
            'new_item' => __('New Testimonial', 'inthub'),
            'view_item' => __('View Testimonial', 'inthub'),
            'search_items' => __('Search Testimonials', 'inthub'),
            'not_found' => __('No testimonials found', 'inthub'),
            'not_found_in_trash' => __('No testimonials found in trash', 'inthub'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-format-quote',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'testimonials'),
    ));

    // Services Post Type
    register_post_type('services', array(
        'labels' => array(
            'name' => __('Services', 'inthub'),
            'singular_name' => __('Service', 'inthub'),
            'add_new' => __('Add New Service', 'inthub'),
            'add_new_item' => __('Add New Service', 'inthub'),
            'edit_item' => __('Edit Service', 'inthub'),
            'new_item' => __('New Service', 'inthub'),
            'view_item' => __('View Service', 'inthub'),
            'search_items' => __('Search Services', 'inthub'),
            'not_found' => __('No services found', 'inthub'),
            'not_found_in_trash' => __('No services found in trash', 'inthub'),
        ),
        'public' => true,
        'has_archive' => true,
        'menu_icon' => 'dashicons-admin-tools',
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt'),
        'rewrite' => array('slug' => 'services'),
    ));

    // Events Post Type
    register_post_type('event', array(
        'labels' => array(
            'name' => __('Sự kiện', 'inthub'),
            'singular_name' => __('Sự kiện', 'inthub'),
            'add_new' => __('Thêm sự kiện mới', 'inthub'),
            'add_new_item' => __('Thêm sự kiện mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa sự kiện', 'inthub'),
            'new_item' => __('Sự kiện mới', 'inthub'),
            'view_item' => __('Xem sự kiện', 'inthub'),
            'search_items' => __('Tìm kiếm sự kiện', 'inthub'),
            'not_found' => __('Không tìm thấy sự kiện nào', 'inthub'),
            'not_found_in_trash' => __('Không có sự kiện nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả sự kiện', 'inthub'),
            'archives' => __('Lưu trữ sự kiện', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-calendar-alt',
        'menu_position' => 5,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'su-kien',
            'with_front' => false,
        ),
    ));

    // University Post Type
    register_post_type('university', array(
        'labels' => array(
            'name' => __('Trường Đại Học', 'inthub'),
            'singular_name' => __('Trường Đại Học', 'inthub'),
            'add_new' => __('Thêm trường mới', 'inthub'),
            'add_new_item' => __('Thêm trường đại học mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa trường đại học', 'inthub'),
            'new_item' => __('Trường đại học mới', 'inthub'),
            'view_item' => __('Xem trường đại học', 'inthub'),
            'search_items' => __('Tìm kiếm trường đại học', 'inthub'),
            'not_found' => __('Không tìm thấy trường đại học nào', 'inthub'),
            'not_found_in_trash' => __('Không có trường đại học nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả trường đại học', 'inthub'),
            'archives' => __('Lưu trữ trường đại học', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-building',
        'menu_position' => 6,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'truong-dai-hoc',
            'with_front' => false,
        ),
    ));

    // Scholarship Post Type
    register_post_type('scholarship', array(
        'labels' => array(
            'name' => __('Học Bổng', 'inthub'),
            'singular_name' => __('Học Bổng', 'inthub'),
            'add_new' => __('Thêm học bổng mới', 'inthub'),
            'add_new_item' => __('Thêm học bổng mới', 'inthub'),
            'edit_item' => __('Chỉnh sửa học bổng', 'inthub'),
            'new_item' => __('Học bổng mới', 'inthub'),
            'view_item' => __('Xem học bổng', 'inthub'),
            'search_items' => __('Tìm kiếm học bổng', 'inthub'),
            'not_found' => __('Không tìm thấy học bổng nào', 'inthub'),
            'not_found_in_trash' => __('Không có học bổng nào trong thùng rác', 'inthub'),
            'all_items' => __('Tất cả học bổng', 'inthub'),
            'archives' => __('Lưu trữ học bổng', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_admin_bar' => true,
        'show_in_rest' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'capability_type' => 'post',
        'menu_icon' => 'dashicons-awards',
        'menu_position' => 7,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'custom-fields'),
        'rewrite' => array(
            'slug' => 'hoc-bong',
            'with_front' => false,
        ),
    ));
}
add_action('init', 'inthub_custom_post_types');

/**
 * Register Custom Taxonomies
 */
function inthub_custom_taxonomies() {
    // University Category Taxonomy
    register_taxonomy('university_category', 'university', array(
        'labels' => array(
            'name' => __('Danh mục trường', 'inthub'),
            'singular_name' => __('Danh mục trường', 'inthub'),
            'menu_name' => __('Danh mục trường', 'inthub'),
            'all_items' => __('Tất cả danh mục', 'inthub'),
            'edit_item' => __('Chỉnh sửa danh mục', 'inthub'),
            'view_item' => __('Xem danh mục', 'inthub'),
            'update_item' => __('Cập nhật danh mục', 'inthub'),
            'add_new_item' => __('Thêm danh mục mới', 'inthub'),
            'new_item_name' => __('Tên danh mục mới', 'inthub'),
            'parent_item' => __('Danh mục cha', 'inthub'),
            'parent_item_colon' => __('Danh mục cha:', 'inthub'),
            'search_items' => __('Tìm kiếm danh mục', 'inthub'),
            'popular_items' => __('Danh mục phổ biến', 'inthub'),
            'separate_items_with_commas' => __('Phân cách danh mục bằng dấu phẩy', 'inthub'),
            'add_or_remove_items' => __('Thêm hoặc xóa danh mục', 'inthub'),
            'choose_from_most_used' => __('Chọn từ danh mục thường dùng', 'inthub'),
            'not_found' => __('Không tìm thấy danh mục nào', 'inthub'),
        ),
        'public' => true,
        'publicly_queryable' => true,
        'hierarchical' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_in_rest' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array(
            'slug' => 'danh-muc-truong',
            'with_front' => false,
            'hierarchical' => true,
        ),
        'capabilities' => array(
            'manage_terms' => 'manage_categories',
            'edit_terms' => 'manage_categories',
            'delete_terms' => 'manage_categories',
            'assign_terms' => 'edit_posts',
        ),
    ));
}
add_action('init', 'inthub_custom_taxonomies');

/**
 * Create Default University Categories
 */
function inthub_create_default_university_categories() {
    // Check if categories already exist
    if (get_option('inthub_university_categories_created')) {
        return;
    }

    // Default categories structure
    $categories = array(
        // Theo loại hình
        'loai-hinh' => array(
            'name' => 'Theo loại hình',
            'description' => 'Phân loại trường đại học theo loại hình đào tạo',
            'children' => array(
                'dai-hoc-cong-lap' => array(
                    'name' => 'Đại học công lập',
                    'description' => 'Các trường đại học công lập được nhà nước tài trợ'
                ),
                'dai-hoc-tu-thuc' => array(
                    'name' => 'Đại học tư thục',
                    'description' => 'Các trường đại học tư thục và tự tài trợ'
                ),
                'cao-dang' => array(
                    'name' => 'Cao đẳng',
                    'description' => 'Các trường cao đẳng và community college'
                ),
                'hoc-vien-chuyen-nganh' => array(
                    'name' => 'Học viện chuyên ngành',
                    'description' => 'Các học viện chuyên về một lĩnh vực cụ thể'
                )
            )
        ),
        // Theo khu vực
        'khu-vuc' => array(
            'name' => 'Theo khu vực',
            'description' => 'Phân loại trường đại học theo khu vực địa lý',
            'children' => array(
                'bac-my' => array(
                    'name' => 'Bắc Mỹ',
                    'description' => 'Các trường đại học tại Hoa Kỳ và Canada'
                ),
                'chau-au' => array(
                    'name' => 'Châu Âu',
                    'description' => 'Các trường đại học tại châu Âu'
                ),
                'chau-a' => array(
                    'name' => 'Châu Á',
                    'description' => 'Các trường đại học tại châu Á'
                ),
                'chau-uc' => array(
                    'name' => 'Châu Úc',
                    'description' => 'Các trường đại học tại Úc và New Zealand'
                )
            )
        ),
        // Theo xếp hạng
        'xep-hang' => array(
            'name' => 'Theo xếp hạng',
            'description' => 'Phân loại trường đại học theo thứ hạng quốc tế',
            'children' => array(
                'top-100' => array(
                    'name' => 'Top 100',
                    'description' => 'Các trường đại học trong top 100 thế giới'
                ),
                'top-500' => array(
                    'name' => 'Top 500',
                    'description' => 'Các trường đại học trong top 500 thế giới'
                ),
                'truong-moi-noi' => array(
                    'name' => 'Trường mới nổi',
                    'description' => 'Các trường đại học mới nổi và đang phát triển'
                )
            )
        )
    );

    // Create parent categories first
    $parent_terms = array();
    foreach ($categories as $slug => $category) {
        $term = wp_insert_term(
            $category['name'],
            'university_category',
            array(
                'slug' => $slug,
                'description' => $category['description']
            )
        );

        if (!is_wp_error($term)) {
            $parent_terms[$slug] = $term['term_id'];
        }
    }

    // Create child categories
    foreach ($categories as $parent_slug => $category) {
        if (isset($category['children']) && isset($parent_terms[$parent_slug])) {
            foreach ($category['children'] as $child_slug => $child_category) {
                wp_insert_term(
                    $child_category['name'],
                    'university_category',
                    array(
                        'slug' => $child_slug,
                        'description' => $child_category['description'],
                        'parent' => $parent_terms[$parent_slug]
                    )
                );
            }
        }
    }

    // Mark as created
    update_option('inthub_university_categories_created', true);
}
add_action('init', 'inthub_create_default_university_categories', 20);

/**
 * Meta Boxes
 */
function inthub_add_meta_boxes() {
    // Event Meta Boxes
    add_meta_box(
        'event_details',
        __('Chi tiết sự kiện', 'inthub'),
        'inthub_event_details_callback',
        'event',
        'normal',
        'high'
    );

    // University Meta Boxes
    add_meta_box(
        'university_details',
        __('Chi tiết trường đại học', 'inthub'),
        'inthub_university_details_callback',
        'university',
        'normal',
        'high'
    );

    // Scholarship Meta Boxes
    add_meta_box(
        'scholarship_details',
        __('Chi tiết học bổng', 'inthub'),
        'inthub_scholarship_details_callback',
        'scholarship',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'inthub_add_meta_boxes');

/**
 * Event Details Meta Box Callback
 */
function inthub_event_details_callback($post) {
    // Add nonce field for security
    wp_nonce_field('inthub_event_meta_nonce', 'inthub_event_meta_nonce_field');

    // Get current values
    $start_datetime = get_post_meta($post->ID, 'event_start_datetime', true);
    $end_datetime = get_post_meta($post->ID, 'event_end_datetime', true);
    $location = get_post_meta($post->ID, 'event_location', true);
    $short_description = get_post_meta($post->ID, 'event_short_description', true);

    // Convert datetime for input fields
    $start_datetime_formatted = $start_datetime ? date('Y-m-d\TH:i', strtotime($start_datetime)) : '';
    $end_datetime_formatted = $end_datetime ? date('Y-m-d\TH:i', strtotime($end_datetime)) : '';
    ?>

    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="event_start_datetime"><?php _e('Thời gian bắt đầu', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="datetime-local"
                       id="event_start_datetime"
                       name="event_start_datetime"
                       value="<?php echo esc_attr($start_datetime_formatted); ?>"
                       required
                       style="width: 300px;" />
                <p class="description"><?php _e('Chọn ngày và giờ bắt đầu sự kiện', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="event_end_datetime"><?php _e('Thời gian kết thúc', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="datetime-local"
                       id="event_end_datetime"
                       name="event_end_datetime"
                       value="<?php echo esc_attr($end_datetime_formatted); ?>"
                       required
                       style="width: 300px;" />
                <p class="description"><?php _e('Chọn ngày và giờ kết thúc sự kiện', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="event_location"><?php _e('Địa điểm', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="event_location"
                       name="event_location"
                       value="<?php echo esc_attr($location); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Nhập địa chỉ tổ chức sự kiện', 'inthub'); ?>" />
                <p class="description"><?php _e('Địa chỉ chi tiết nơi tổ chức sự kiện', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="event_short_description"><?php _e('Mô tả ngắn', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <textarea id="event_short_description"
                          name="event_short_description"
                          rows="3"
                          style="width: 100%;"
                          maxlength="200"
                          required
                          placeholder="<?php _e('Mô tả ngắn gọn về sự kiện (tối đa 200 ký tự)', 'inthub'); ?>"><?php echo esc_textarea($short_description); ?></textarea>
                <p class="description">
                    <?php _e('Mô tả ngắn gọn về sự kiện (tối đa 200 ký tự)', 'inthub'); ?>
                    <span id="char-count"><?php echo strlen($short_description); ?>/200</span>
                </p>
            </td>
        </tr>
    </table>

    <script>
    jQuery(document).ready(function($) {
        // Character counter for short description
        $('#event_short_description').on('input', function() {
            var length = $(this).val().length;
            $('#char-count').text(length + '/200');

            if (length > 200) {
                $('#char-count').css('color', 'red');
            } else {
                $('#char-count').css('color', 'inherit');
            }
        });

        // Validate datetime fields
        $('#event_start_datetime, #event_end_datetime').on('change', function() {
            var startDate = new Date($('#event_start_datetime').val());
            var endDate = new Date($('#event_end_datetime').val());

            if (startDate && endDate && startDate >= endDate) {
                alert('<?php _e('Thời gian kết thúc phải sau thời gian bắt đầu!', 'inthub'); ?>');
                $(this).focus();
            }
        });
    });
    </script>

    <?php
}

/**
 * University Details Meta Box Callback
 */
function inthub_university_details_callback($post) {
    // Add nonce field for security
    wp_nonce_field('inthub_university_meta_nonce', 'inthub_university_meta_nonce_field');

    // Get current values
    $university_name = get_post_meta($post->ID, 'university_name', true);
    $establishment_year = get_post_meta($post->ID, 'establishment_year', true);
    $location = get_post_meta($post->ID, 'location', true);
    $overview = get_post_meta($post->ID, 'overview', true);
    $website_url = get_post_meta($post->ID, 'website_url', true);
    $courses = get_post_meta($post->ID, 'courses', true);
    $country = get_post_meta($post->ID, 'country', true);
    $tuition_fee = get_post_meta($post->ID, 'tuition_fee', true);
    $admission_info = get_post_meta($post->ID, 'admission_info', true);
    $map = get_post_meta($post->ID, 'map', true);
    $video_url = get_post_meta($post->ID, 'video_url', true);
    ?>

    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="university_name"><?php _e('Tên trường đại học', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="university_name"
                       name="university_name"
                       value="<?php echo esc_attr($university_name); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Nhập tên trường đại học', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="establishment_year"><?php _e('Năm thành lập', 'inthub'); ?></label>
            </th>
            <td>
                <input type="number"
                       id="establishment_year"
                       name="establishment_year"
                       value="<?php echo esc_attr($establishment_year); ?>"
                       min="1000"
                       max="<?php echo date('Y'); ?>"
                       style="width: 200px;"
                       placeholder="<?php _e('Ví dụ: 1990', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="location"><?php _e('Địa điểm', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="location"
                       name="location"
                       value="<?php echo esc_attr($location); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Nhập địa chỉ trường đại học', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="country"><?php _e('Quốc gia', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <select id="country" name="country" required style="width: 300px;">
                    <option value=""><?php _e('Chọn quốc gia', 'inthub'); ?></option>
                    <option value="usa" <?php selected($country, 'usa'); ?>><?php _e('Hoa Kỳ', 'inthub'); ?></option>
                    <option value="uk" <?php selected($country, 'uk'); ?>><?php _e('Anh', 'inthub'); ?></option>
                    <option value="canada" <?php selected($country, 'canada'); ?>><?php _e('Canada', 'inthub'); ?></option>
                    <option value="australia" <?php selected($country, 'australia'); ?>><?php _e('Úc', 'inthub'); ?></option>
                    <option value="germany" <?php selected($country, 'germany'); ?>><?php _e('Đức', 'inthub'); ?></option>
                    <option value="france" <?php selected($country, 'france'); ?>><?php _e('Pháp', 'inthub'); ?></option>
                    <option value="japan" <?php selected($country, 'japan'); ?>><?php _e('Nhật Bản', 'inthub'); ?></option>
                    <option value="south_korea" <?php selected($country, 'south_korea'); ?>><?php _e('Hàn Quốc', 'inthub'); ?></option>
                    <option value="singapore" <?php selected($country, 'singapore'); ?>><?php _e('Singapore', 'inthub'); ?></option>
                    <option value="netherlands" <?php selected($country, 'netherlands'); ?>><?php _e('Hà Lan', 'inthub'); ?></option>
                    <option value="other" <?php selected($country, 'other'); ?>><?php _e('Khác', 'inthub'); ?></option>
                </select>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="website_url"><?php _e('Website', 'inthub'); ?></label>
            </th>
            <td>
                <input type="url"
                       id="website_url"
                       name="website_url"
                       value="<?php echo esc_attr($website_url); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('https://example.edu', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="tuition_fee"><?php _e('Học phí', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="tuition_fee"
                       name="tuition_fee"
                       value="<?php echo esc_attr($tuition_fee); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: $20,000 - $30,000/năm', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="video_url"><?php _e('Link video giới thiệu', 'inthub'); ?></label>
            </th>
            <td>
                <input type="url"
                       id="video_url"
                       name="video_url"
                       value="<?php echo esc_attr($video_url); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('https://youtube.com/watch?v=...', 'inthub'); ?>" />
            </td>
        </tr>
    </table>

    <h3><?php _e('Thông tin chi tiết', 'inthub'); ?></h3>
    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="overview"><?php _e('Tổng quan', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($overview, 'overview', array(
                    'textarea_name' => 'overview',
                    'media_buttons' => true,
                    'textarea_rows' => 8,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Mô tả tổng quan về trường đại học', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="courses"><?php _e('Khóa học', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="courses"
                          name="courses"
                          rows="5"
                          style="width: 100%;"
                          placeholder="<?php _e('Liệt kê các khóa học chính (mỗi khóa học một dòng)', 'inthub'); ?>"><?php echo esc_textarea($courses); ?></textarea>
                <p class="description"><?php _e('Danh sách các khóa học chính của trường', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="admission_info"><?php _e('Thông tin tuyển sinh', 'inthub'); ?></label>
            </th>
            <td>
                <?php
                wp_editor($admission_info, 'admission_info', array(
                    'textarea_name' => 'admission_info',
                    'media_buttons' => true,
                    'textarea_rows' => 6,
                    'teeny' => false,
                    'tinymce' => true,
                    'quicktags' => true
                ));
                ?>
                <p class="description"><?php _e('Thông tin về yêu cầu tuyển sinh và quy trình đăng ký', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="map"><?php _e('Bản đồ (Google Maps Embed)', 'inthub'); ?></label>
            </th>
            <td>
                <textarea id="map"
                          name="map"
                          rows="3"
                          style="width: 100%;"
                          placeholder="<?php _e('Dán mã embed Google Maps tại đây', 'inthub'); ?>"><?php echo esc_textarea($map); ?></textarea>
                <p class="description"><?php _e('Mã embed Google Maps để hiển thị vị trí trường', 'inthub'); ?></p>
            </td>
        </tr>
    </table>

    <?php
}

/**
 * Scholarship Details Meta Box Callback
 */
function inthub_scholarship_details_callback($post) {
    // Add nonce field for security
    wp_nonce_field('inthub_scholarship_meta_nonce', 'inthub_scholarship_meta_nonce_field');

    // Get current values
    $scholarship_name = get_post_meta($post->ID, 'scholarship_name', true);
    $tuition_support = get_post_meta($post->ID, 'tuition_support', true);
    $scholarship_value = get_post_meta($post->ID, 'scholarship_value', true);
    $related_university = get_post_meta($post->ID, 'related_university', true);

    // Get new repeater fields
    $application_requirements = get_post_meta($post->ID, 'application_requirements', true);
    $application_process = get_post_meta($post->ID, 'application_process', true);

    // Ensure arrays exist
    if (!is_array($application_requirements)) {
        $application_requirements = array();
    }
    if (!is_array($application_process)) {
        $application_process = array();
    }

    // Get all universities for dropdown
    $universities = get_posts(array(
        'post_type' => 'university',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'orderby' => 'title',
        'order' => 'ASC'
    ));

    // Enqueue admin scripts and styles
    wp_enqueue_script('inthub-admin-scholarship', get_template_directory_uri() . '/assets/js/admin-scholarship.js', array('jquery', 'jquery-ui-sortable'), '1.0.0', true);
    wp_enqueue_style('inthub-admin-scholarship', get_template_directory_uri() . '/assets/css/admin-scholarship.css', array(), '1.0.0');
    ?>

    <table class="form-table">
        <tr>
            <th scope="row">
                <label for="scholarship_name"><?php _e('Tên học bổng', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="scholarship_name"
                       name="scholarship_name"
                       value="<?php echo esc_attr($scholarship_name); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Nhập tên học bổng', 'inthub'); ?>" />
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="related_university"><?php _e('Trường đại học liên quan', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <select id="related_university" name="related_university" required style="width: 100%;">
                    <option value=""><?php _e('Chọn trường đại học', 'inthub'); ?></option>
                    <?php foreach ($universities as $university): ?>
                        <option value="<?php echo $university->ID; ?>" <?php selected($related_university, $university->ID); ?>>
                            <?php echo esc_html($university->post_title); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
                <p class="description"><?php _e('Chọn trường đại học mà học bổng này thuộc về', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="scholarship_value"><?php _e('Giá trị học bổng', 'inthub'); ?> <span style="color: red;">*</span></label>
            </th>
            <td>
                <input type="text"
                       id="scholarship_value"
                       name="scholarship_value"
                       value="<?php echo esc_attr($scholarship_value); ?>"
                       required
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: $10,000/năm hoặc 50% học phí', 'inthub'); ?>" />
                <p class="description"><?php _e('Mô tả giá trị của học bổng', 'inthub'); ?></p>
            </td>
        </tr>
        <tr>
            <th scope="row">
                <label for="tuition_support"><?php _e('Học phí được hỗ trợ', 'inthub'); ?></label>
            </th>
            <td>
                <input type="text"
                       id="tuition_support"
                       name="tuition_support"
                       value="<?php echo esc_attr($tuition_support); ?>"
                       style="width: 100%;"
                       placeholder="<?php _e('Ví dụ: Toàn bộ học phí hoặc 75% học phí', 'inthub'); ?>" />
                <p class="description"><?php _e('Mô tả cụ thể về phần học phí được hỗ trợ', 'inthub'); ?></p>
            </td>
        </tr>
    </table>

    <!-- Application Requirements Repeater -->
    <h3><?php _e('Yêu cầu ứng tuyển', 'inthub'); ?></h3>
    <div id="application-requirements-repeater" class="inthub-repeater">
        <div class="repeater-items" data-field-type="requirements">
            <?php if (!empty($application_requirements)) : ?>
                <?php foreach ($application_requirements as $index => $requirement) :
                    // Handle backward compatibility - convert old string format to new object format
                    if (is_string($requirement)) {
                        $requirement = array(
                            'title' => '',
                            'description' => $requirement
                        );
                    }
                ?>
                    <div class="repeater-item" data-index="<?php echo $index; ?>">
                        <div class="repeater-content">
                            <input type="text"
                                   name="application_requirements[<?php echo $index; ?>][title]"
                                   placeholder="<?php _e('Tiêu đề yêu cầu...', 'inthub'); ?>"
                                   value="<?php echo esc_attr(isset($requirement['title']) ? $requirement['title'] : ''); ?>" />
                            <textarea name="application_requirements[<?php echo $index; ?>][description]"
                                    placeholder="<?php _e('Mô tả chi tiết yêu cầu...', 'inthub'); ?>"
                                    rows="3"><?php echo esc_textarea(isset($requirement['description']) ? $requirement['description'] : ''); ?></textarea>
                        </div>
                        <div class="repeater-actions">
                            <button type="button" class="button remove-item"><?php _e('Xóa', 'inthub'); ?></button>
                            <span class="sort-handle" title="<?php _e('Kéo để sắp xếp', 'inthub'); ?>">⋮⋮</span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <button type="button" class="button add-item" data-target="application-requirements">
            <?php _e('Thêm yêu cầu', 'inthub'); ?>
        </button>
        <p class="description"><?php _e('Thêm các yêu cầu cụ thể mà ứng viên cần đáp ứng để ứng tuyển học bổng này.', 'inthub'); ?></p>
    </div>

    <!-- Application Process Repeater -->
    <h3><?php _e('Quy trình ứng tuyển', 'inthub'); ?></h3>
    <div id="application-process-repeater" class="inthub-repeater">
        <div class="repeater-items" data-field-type="process">
            <?php if (!empty($application_process)) : ?>
                <?php foreach ($application_process as $index => $step) : ?>
                    <div class="repeater-item" data-index="<?php echo $index; ?>">
                        <div class="repeater-content">
                            <input type="text"
                                   name="application_process[<?php echo $index; ?>][title]"
                                   placeholder="<?php _e('Tiêu đề bước...', 'inthub'); ?>"
                                   value="<?php echo esc_attr(isset($step['title']) ? $step['title'] : ''); ?>" />
                            <textarea name="application_process[<?php echo $index; ?>][description]"
                                    placeholder="<?php _e('Mô tả chi tiết bước...', 'inthub'); ?>"
                                    rows="3"><?php echo esc_textarea(isset($step['description']) ? $step['description'] : ''); ?></textarea>
                        </div>
                        <div class="repeater-actions">
                            <button type="button" class="button remove-item"><?php _e('Xóa', 'inthub'); ?></button>
                            <span class="sort-handle" title="<?php _e('Kéo để sắp xếp', 'inthub'); ?>">⋮⋮</span>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
        <button type="button" class="button add-item" data-target="application-process">
            <?php _e('Thêm bước', 'inthub'); ?>
        </button>
        <p class="description"><?php _e('Thêm các bước trong quy trình ứng tuyển học bổng theo thứ tự thời gian.', 'inthub'); ?></p>
    </div>

    <?php
}

/**
 * Save Event Meta Data
 */
function inthub_save_event_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['inthub_event_meta_nonce_field']) ||
        !wp_verify_nonce($_POST['inthub_event_meta_nonce_field'], 'inthub_event_meta_nonce')) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Save meta fields
    $fields = array(
        'event_start_datetime',
        'event_end_datetime',
        'event_location',
        'event_short_description'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = sanitize_text_field($_POST[$field]);

            // Additional validation for datetime fields
            if (in_array($field, array('event_start_datetime', 'event_end_datetime'))) {
                $value = sanitize_text_field($_POST[$field]);
                // Convert to MySQL datetime format
                if ($value) {
                    $value = date('Y-m-d H:i:s', strtotime($value));
                }
            }

            // Additional validation for short description
            if ($field === 'event_short_description') {
                $value = substr(sanitize_textarea_field($_POST[$field]), 0, 200);
            }

            update_post_meta($post_id, $field, $value);
        }
    }

    // Validate required fields
    $required_fields = array('event_start_datetime', 'event_end_datetime', 'event_location', 'event_short_description');
    foreach ($required_fields as $field) {
        if (empty($_POST[$field])) {
            // You could add admin notices here for validation errors
            continue;
        }
    }
}
add_action('save_post', 'inthub_save_event_meta');

/**
 * Save University Meta Data
 */
function inthub_save_university_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['inthub_university_meta_nonce_field']) ||
        !wp_verify_nonce($_POST['inthub_university_meta_nonce_field'], 'inthub_university_meta_nonce')) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Save meta fields
    $fields = array(
        'university_name',
        'establishment_year',
        'location',
        'overview',
        'website_url',
        'courses',
        'country',
        'tuition_fee',
        'admission_info',
        'map',
        'video_url'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];

            // Sanitize based on field type
            switch ($field) {
                case 'establishment_year':
                    $value = intval($value);
                    break;
                case 'website_url':
                case 'video_url':
                    $value = esc_url_raw($value);
                    break;
                case 'overview':
                case 'admission_info':
                    $value = wp_kses_post($value);
                    break;
                case 'courses':
                case 'map':
                    $value = sanitize_textarea_field($value);
                    break;
                default:
                    $value = sanitize_text_field($value);
                    break;
            }

            update_post_meta($post_id, $field, $value);
        }
    }
}
add_action('save_post', 'inthub_save_university_meta');

/**
 * Save Scholarship Meta Data
 */
function inthub_save_scholarship_meta($post_id) {
    // Check if nonce is valid
    if (!isset($_POST['inthub_scholarship_meta_nonce_field']) ||
        !wp_verify_nonce($_POST['inthub_scholarship_meta_nonce_field'], 'inthub_scholarship_meta_nonce')) {
        return;
    }

    // Check if user has permission to edit post
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Check if this is an autosave
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Save meta fields
    $fields = array(
        'scholarship_name',
        'tuition_support',
        'scholarship_value',
        'related_university'
    );

    foreach ($fields as $field) {
        if (isset($_POST[$field])) {
            $value = $_POST[$field];

            // Sanitize based on field type
            switch ($field) {
                case 'related_university':
                    $value = intval($value);
                    break;
                default:
                    $value = sanitize_text_field($value);
                    break;
            }

            update_post_meta($post_id, $field, $value);
        }
    }

    // Save Application Requirements (repeater field)
    if (isset($_POST['application_requirements'])) {
        $requirements = array();
        foreach ($_POST['application_requirements'] as $requirement) {
            $sanitized_requirement = array(
                'title' => sanitize_text_field($requirement['title']),
                'description' => sanitize_textarea_field($requirement['description'])
            );
            if (!empty($sanitized_requirement['title']) || !empty($sanitized_requirement['description'])) {
                $requirements[] = $sanitized_requirement;
            }
        }
        update_post_meta($post_id, 'application_requirements', $requirements);
    } else {
        delete_post_meta($post_id, 'application_requirements');
    }

    // Save Application Process (repeater field)
    if (isset($_POST['application_process'])) {
        $process_steps = array();
        foreach ($_POST['application_process'] as $step) {
            $sanitized_step = array(
                'title' => sanitize_text_field($step['title']),
                'description' => sanitize_textarea_field($step['description'])
            );
            if (!empty($sanitized_step['title']) || !empty($sanitized_step['description'])) {
                $process_steps[] = $sanitized_step;
            }
        }
        update_post_meta($post_id, 'application_process', $process_steps);
    } else {
        delete_post_meta($post_id, 'application_process');
    }
}
add_action('save_post', 'inthub_save_scholarship_meta');

/**
 * Helper Functions for Scholarship Custom Fields
 */

/**
 * Get and display application requirements
 */
function inthub_get_application_requirements($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $requirements = get_post_meta($post_id, 'application_requirements', true);

    if (empty($requirements) || !is_array($requirements)) {
        return false;
    }

    // Handle backward compatibility - convert old string format to new object format
    $normalized_requirements = array();
    foreach ($requirements as $requirement) {
        if (is_string($requirement)) {
            // Old format: just a string
            $normalized_requirements[] = array(
                'title' => '',
                'description' => $requirement
            );
        } elseif (is_array($requirement)) {
            // New format: object with title and description
            $normalized_requirements[] = array(
                'title' => isset($requirement['title']) ? $requirement['title'] : '',
                'description' => isset($requirement['description']) ? $requirement['description'] : ''
            );
        }
    }

    return $normalized_requirements;
}

/**
 * Display application requirements as HTML list
 */
function inthub_display_application_requirements($post_id = null, $class = 'application-requirements-list') {
    $requirements = inthub_get_application_requirements($post_id);

    if (!$requirements) {
        return '';
    }

    $output = '<div class="' . esc_attr($class) . '">';
    $output .= '<ol class="requirements-list">';

    foreach ($requirements as $index => $requirement) {
        $title = isset($requirement['title']) ? $requirement['title'] : '';
        $description = isset($requirement['description']) ? $requirement['description'] : '';

        if (empty($title) && empty($description)) {
            continue;
        }

        $output .= '<li class="requirement-item">';
        $output .= '<div class="requirement-content">';

        if (!empty($title)) {
            $output .= '<h4 class="requirement-title">' . esc_html($title) . '</h4>';
        }

        if (!empty($description)) {
            $output .= '<div class="requirement-description">' . wp_kses_post(wpautop($description)) . '</div>';
        }

        $output .= '</div>';
        $output .= '</li>';
    }

    $output .= '</ol>';
    $output .= '</div>';

    return $output;
}

/**
 * Get and display application process steps
 */
function inthub_get_application_process($post_id = null) {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $process = get_post_meta($post_id, 'application_process', true);

    if (empty($process) || !is_array($process)) {
        return false;
    }

    return $process;
}

/**
 * Display application process as HTML timeline
 */
function inthub_display_application_process($post_id = null, $class = 'application-process-timeline') {
    $process = inthub_get_application_process($post_id);

    if (!$process) {
        return '';
    }

    $output = '<div class="' . esc_attr($class) . '">';

    foreach ($process as $index => $step) {
        $step_number = $index + 1;
        $title = isset($step['title']) ? $step['title'] : '';
        $description = isset($step['description']) ? $step['description'] : '';

        if (empty($title) && empty($description)) {
            continue;
        }

        $output .= '<div class="process-step" data-step="' . $step_number . '">';
        $output .= '<div class="step-number">' . $step_number . '</div>';
        $output .= '<div class="step-content">';

        if (!empty($title)) {
            $output .= '<h3 class="step-title">' . esc_html($title) . '</h3>';
        }

        if (!empty($description)) {
            $output .= '<div class="step-description">' . wp_kses_post(wpautop($description)) . '</div>';
        }

        $output .= '</div>';
        $output .= '</div>';
    }

    $output .= '</div>';

    return $output;
}

/**
 * Enqueue Scholarship Frontend Styles
 */
function inthub_enqueue_scholarship_styles() {
    if (is_singular('scholarship')) {
        wp_enqueue_style(
            'inthub-scholarship-frontend',
            get_template_directory_uri() . '/assets/css/scholarship-frontend.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_scholarship_styles');

/**
 * University Taxonomy Helper Functions
 */

/**
 * Get university categories for a specific university
 */
function inthub_get_university_categories($post_id = null, $format = 'objects') {
    if (!$post_id) {
        $post_id = get_the_ID();
    }

    $categories = get_the_terms($post_id, 'university_category');

    if (!$categories || is_wp_error($categories)) {
        return false;
    }

    if ($format === 'names') {
        return wp_list_pluck($categories, 'name');
    } elseif ($format === 'slugs') {
        return wp_list_pluck($categories, 'slug');
    }

    return $categories;
}

/**
 * Display university categories as HTML
 */
function inthub_display_university_categories($post_id = null, $class = 'university-categories', $link = true) {
    $categories = inthub_get_university_categories($post_id);

    if (!$categories) {
        return '';
    }

    $output = '<div class="' . esc_attr($class) . '">';

    foreach ($categories as $category) {
        if ($link) {
            $output .= '<a href="' . get_term_link($category) . '" class="category-link">';
            $output .= esc_html($category->name);
            $output .= '</a>';
        } else {
            $output .= '<span class="category-item">' . esc_html($category->name) . '</span>';
        }
    }

    $output .= '</div>';

    return $output;
}

/**
 * Get universities by category
 */
function inthub_get_universities_by_category($category_slug, $args = array()) {
    $default_args = array(
        'post_type' => 'university',
        'posts_per_page' => -1,
        'post_status' => 'publish',
        'tax_query' => array(
            array(
                'taxonomy' => 'university_category',
                'field' => 'slug',
                'terms' => $category_slug,
            ),
        ),
    );

    $args = wp_parse_args($args, $default_args);

    return get_posts($args);
}

/**
 * Get category hierarchy for breadcrumbs
 */
function inthub_get_category_breadcrumb($term_id) {
    $breadcrumb = array();
    $term = get_term($term_id, 'university_category');

    if (!$term || is_wp_error($term)) {
        return $breadcrumb;
    }

    // Build breadcrumb array
    while ($term && !is_wp_error($term)) {
        array_unshift($breadcrumb, array(
            'name' => $term->name,
            'url' => get_term_link($term),
            'id' => $term->term_id
        ));

        if ($term->parent) {
            $term = get_term($term->parent, 'university_category');
        } else {
            break;
        }
    }

    return $breadcrumb;
}

/**
 * Enqueue university taxonomy styles
 */
function inthub_enqueue_university_taxonomy_styles() {
    if (is_tax('university_category') || is_post_type_archive('university') || is_singular('university')) {
        wp_enqueue_style(
            'inthub-university-taxonomy',
            get_template_directory_uri() . '/assets/css/university-taxonomy.css',
            array(),
            '1.0.0'
        );
    }
}
add_action('wp_enqueue_scripts', 'inthub_enqueue_university_taxonomy_styles');

/**
 * AJAX Load More Universities
 */
function inthub_load_more_universities() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die('Security check failed');
    }

    $page = intval($_POST['page']);
    $country = sanitize_text_field($_POST['country']);
    $search = sanitize_text_field($_POST['search']);

    $args = array(
        'post_type' => 'university',
        'posts_per_page' => 9,
        'paged' => $page,
        'post_status' => 'publish'
    );

    // Add meta query for country filter
    if (!empty($country)) {
        $args['meta_query'] = array(
            array(
                'key' => 'country',
                'value' => $country,
                'compare' => '='
            )
        );
    }

    // Add search query
    if (!empty($search)) {
        $args['s'] = $search;
    }

    $query = new WP_Query($args);

    ob_start();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            // Include the university card template
            get_template_part('template-parts/university-card');
        }
    }

    $html = ob_get_clean();
    wp_reset_postdata();

    wp_send_json_success(array(
        'html' => $html,
        'has_more' => $page < $query->max_num_pages
    ));
}
add_action('wp_ajax_load_more_universities', 'inthub_load_more_universities');
add_action('wp_ajax_nopriv_load_more_universities', 'inthub_load_more_universities');

/**
 * AJAX Load More Scholarships
 */
function inthub_load_more_scholarships() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'inthub_nonce')) {
        wp_die('Security check failed');
    }

    $page = intval($_POST['page']);
    $university = intval($_POST['university']);
    $country = sanitize_text_field($_POST['country']);
    $value = sanitize_text_field($_POST['value']);
    $search = sanitize_text_field($_POST['search']);

    $args = array(
        'post_type' => 'scholarship',
        'posts_per_page' => 9,
        'paged' => $page,
        'post_status' => 'publish'
    );

    $meta_query = array();

    // Add university filter
    if (!empty($university)) {
        $meta_query[] = array(
            'key' => 'related_university',
            'value' => $university,
            'compare' => '='
        );
    }

    // Add country filter (through university relationship)
    if (!empty($country)) {
        $university_ids = get_posts(array(
            'post_type' => 'university',
            'posts_per_page' => -1,
            'fields' => 'ids',
            'meta_query' => array(
                array(
                    'key' => 'country',
                    'value' => $country,
                    'compare' => '='
                )
            )
        ));

        if (!empty($university_ids)) {
            $meta_query[] = array(
                'key' => 'related_university',
                'value' => $university_ids,
                'compare' => 'IN'
            );
        }
    }

    if (!empty($meta_query)) {
        $args['meta_query'] = $meta_query;
    }

    // Add search query
    if (!empty($search)) {
        $args['s'] = $search;
    }

    $query = new WP_Query($args);

    ob_start();

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            // Include the scholarship card template
            get_template_part('template-parts/scholarship-card');
        }
    }

    $html = ob_get_clean();
    wp_reset_postdata();

    wp_send_json_success(array(
        'html' => $html,
        'has_more' => $page < $query->max_num_pages
    ));
}
add_action('wp_ajax_load_more_scholarships', 'inthub_load_more_scholarships');
add_action('wp_ajax_nopriv_load_more_scholarships', 'inthub_load_more_scholarships');

/**
 * Add Custom Columns to University Admin List
 */
function inthub_university_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['university_country'] = __('Quốc gia', 'inthub');
    $new_columns['university_location'] = __('Địa điểm', 'inthub');
    $new_columns['establishment_year'] = __('Năm thành lập', 'inthub');
    $new_columns['scholarships_count'] = __('Số học bổng', 'inthub');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_university_posts_columns', 'inthub_university_admin_columns');

/**
 * Populate Custom Columns in University Admin List
 */
function inthub_university_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'university_country':
            $country = get_post_meta($post_id, 'country', true);
            $country_names = array(
                'usa' => 'Hoa Kỳ',
                'uk' => 'Anh',
                'canada' => 'Canada',
                'australia' => 'Úc',
                'germany' => 'Đức',
                'france' => 'Pháp',
                'japan' => 'Nhật Bản',
                'south_korea' => 'Hàn Quốc',
                'singapore' => 'Singapore',
                'netherlands' => 'Hà Lan',
                'other' => 'Khác'
            );
            $country_display = isset($country_names[$country]) ? $country_names[$country] : $country;
            echo $country_display ? '<span class="country-flag">' . esc_html($country_display) . '</span>' : '<span style="color: #999;">—</span>';
            break;

        case 'university_location':
            $location = get_post_meta($post_id, 'location', true);
            if ($location) {
                echo '<span title="' . esc_attr($location) . '">' .
                     (strlen($location) > 30 ? substr($location, 0, 30) . '...' : $location) .
                     '</span>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'establishment_year':
            $year = get_post_meta($post_id, 'establishment_year', true);
            echo $year ? esc_html($year) : '<span style="color: #999;">—</span>';
            break;

        case 'scholarships_count':
            $scholarships = get_posts(array(
                'post_type' => 'scholarship',
                'meta_query' => array(
                    array(
                        'key' => 'related_university',
                        'value' => $post_id,
                        'compare' => '='
                    )
                ),
                'posts_per_page' => -1,
                'fields' => 'ids'
            ));
            $count = count($scholarships);
            echo '<strong>' . $count . '</strong>';
            if ($count > 0) {
                echo ' <a href="' . admin_url('edit.php?post_type=scholarship&university=' . $post_id) . '" class="button button-small">Xem</a>';
            }
            break;
    }
}
add_action('manage_university_posts_custom_column', 'inthub_university_admin_column_content', 10, 2);

/**
 * Add Custom Columns to Scholarship Admin List
 */
function inthub_scholarship_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['related_university'] = __('Trường đại học', 'inthub');
    $new_columns['scholarship_value'] = __('Giá trị', 'inthub');
    $new_columns['tuition_support'] = __('Hỗ trợ học phí', 'inthub');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_scholarship_posts_columns', 'inthub_scholarship_admin_columns');

/**
 * Populate Custom Columns in Scholarship Admin List
 */
function inthub_scholarship_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'related_university':
            $university_id = get_post_meta($post_id, 'related_university', true);
            if ($university_id) {
                $university = get_post($university_id);
                if ($university) {
                    $university_name = get_post_meta($university_id, 'university_name', true) ?: $university->post_title;
                    echo '<a href="' . get_edit_post_link($university_id) . '">' . esc_html($university_name) . '</a>';
                } else {
                    echo '<span style="color: #d63638;">Trường đã bị xóa</span>';
                }
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'scholarship_value':
            $value = get_post_meta($post_id, 'scholarship_value', true);
            if ($value) {
                echo '<strong>' . esc_html($value) . '</strong>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'tuition_support':
            $support = get_post_meta($post_id, 'tuition_support', true);
            if ($support) {
                echo '<span title="' . esc_attr($support) . '">' .
                     (strlen($support) > 30 ? substr($support, 0, 30) . '...' : $support) .
                     '</span>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;
    }
}
add_action('manage_scholarship_posts_custom_column', 'inthub_scholarship_admin_column_content', 10, 2);

/**
 * Add Custom Columns to Events Admin List
 */
function inthub_event_admin_columns($columns) {
    $new_columns = array();
    $new_columns['cb'] = $columns['cb'];
    $new_columns['title'] = $columns['title'];
    $new_columns['event_start_date'] = __('Thời gian bắt đầu', 'inthub');
    $new_columns['event_end_date'] = __('Thời gian kết thúc', 'inthub');
    $new_columns['event_location'] = __('Địa điểm', 'inthub');
    $new_columns['event_status'] = __('Trạng thái', 'inthub');
    $new_columns['date'] = $columns['date'];

    return $new_columns;
}
add_filter('manage_event_posts_columns', 'inthub_event_admin_columns');

/**
 * Populate Custom Columns in Events Admin List
 */
function inthub_event_admin_column_content($column, $post_id) {
    switch ($column) {
        case 'event_start_date':
            $start_datetime = get_post_meta($post_id, 'event_start_datetime', true);
            if ($start_datetime) {
                echo '<strong>' . date('d/m/Y', strtotime($start_datetime)) . '</strong><br>';
                echo '<small>' . date('H:i', strtotime($start_datetime)) . '</small>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'event_end_date':
            $end_datetime = get_post_meta($post_id, 'event_end_datetime', true);
            if ($end_datetime) {
                echo '<strong>' . date('d/m/Y', strtotime($end_datetime)) . '</strong><br>';
                echo '<small>' . date('H:i', strtotime($end_datetime)) . '</small>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'event_location':
            $location = get_post_meta($post_id, 'event_location', true);
            if ($location) {
                echo '<span title="' . esc_attr($location) . '">' .
                     (strlen($location) > 30 ? substr($location, 0, 30) . '...' : $location) .
                     '</span>';
            } else {
                echo '<span style="color: #999;">—</span>';
            }
            break;

        case 'event_status':
            $start_datetime = get_post_meta($post_id, 'event_start_datetime', true);
            $end_datetime = get_post_meta($post_id, 'event_end_datetime', true);

            if ($start_datetime && $end_datetime) {
                $now = current_time('timestamp');
                $start_time = strtotime($start_datetime);
                $end_time = strtotime($end_datetime);

                if ($now < $start_time) {
                    echo '<span style="color: #0073aa; font-weight: bold;">Sắp diễn ra</span>';
                } elseif ($now >= $start_time && $now <= $end_time) {
                    echo '<span style="color: #00a32a; font-weight: bold;">Đang diễn ra</span>';
                } else {
                    echo '<span style="color: #999;">Đã kết thúc</span>';
                }
            } else {
                echo '<span style="color: #d63638;">Chưa đặt lịch</span>';
            }
            break;
    }
}
add_action('manage_event_posts_custom_column', 'inthub_event_admin_column_content', 10, 2);

/**
 * Make Event Columns Sortable
 */
function inthub_event_sortable_columns($columns) {
    $columns['event_start_date'] = 'event_start_datetime';
    $columns['event_end_date'] = 'event_end_datetime';
    $columns['event_location'] = 'event_location';
    return $columns;
}
add_filter('manage_edit-event_sortable_columns', 'inthub_event_sortable_columns');

/**
 * Handle Sorting for Event Columns
 */
function inthub_event_orderby($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    $orderby = $query->get('orderby');

    if ('event_start_datetime' === $orderby) {
        $query->set('meta_key', 'event_start_datetime');
        $query->set('orderby', 'meta_value');
    } elseif ('event_end_datetime' === $orderby) {
        $query->set('meta_key', 'event_end_datetime');
        $query->set('orderby', 'meta_value');
    } elseif ('event_location' === $orderby) {
        $query->set('meta_key', 'event_location');
        $query->set('orderby', 'meta_value');
    }
}
add_action('pre_get_posts', 'inthub_event_orderby');

/**
 * Default Sort Events by Start Date
 */
function inthub_event_default_sort($query) {
    if (!is_admin() || !$query->is_main_query()) {
        return;
    }

    if ($query->get('post_type') === 'event' && !$query->get('orderby')) {
        $query->set('meta_key', 'event_start_datetime');
        $query->set('orderby', 'meta_value');
        $query->set('order', 'ASC');
    }
}
add_action('pre_get_posts', 'inthub_event_default_sort');

/**
 * Enqueue Admin Scripts for Events
 */
function inthub_event_admin_scripts($hook) {
    global $post_type;

    if ($post_type === 'event' && in_array($hook, array('post.php', 'post-new.php'))) {
        wp_enqueue_script('jquery');
        wp_enqueue_script('inthub-event-admin', get_template_directory_uri() . '/assets/js/main.js', array('jquery'), '1.0.0', true);

        // Localize script for translations
        wp_localize_script('inthub-event-admin', 'inthub_event_admin', array(
            'end_time_error' => __('Thời gian kết thúc phải sau thời gian bắt đầu!', 'inthub'),
            'required_field_error' => __('Trường này là bắt buộc', 'inthub'),
            'char_limit_error' => __('Vượt quá giới hạn ký tự cho phép', 'inthub'),
        ));
    }
}
add_action('admin_enqueue_scripts', 'inthub_event_admin_scripts');

/**
 * Add Event Query Vars for Frontend Filtering
 */
function inthub_event_query_vars($vars) {
    $vars[] = 'event_status';
    $vars[] = 'event_location';
    return $vars;
}
add_filter('query_vars', 'inthub_event_query_vars');

/**
 * Modify Event Archive Query
 */
function inthub_event_archive_query($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('event')) {
        // Default sort by start date
        $query->set('meta_key', 'event_start_datetime');
        $query->set('orderby', 'meta_value');
        $query->set('order', 'ASC');

        // Filter by event status if requested
        $event_status = get_query_var('event_status');
        if ($event_status) {
            $meta_query = array();
            $now = current_time('mysql');

            switch ($event_status) {
                case 'upcoming':
                    $meta_query[] = array(
                        'key' => 'event_start_datetime',
                        'value' => $now,
                        'compare' => '>',
                        'type' => 'DATETIME'
                    );
                    break;
                case 'ongoing':
                    $meta_query[] = array(
                        'relation' => 'AND',
                        array(
                            'key' => 'event_start_datetime',
                            'value' => $now,
                            'compare' => '<=',
                            'type' => 'DATETIME'
                        ),
                        array(
                            'key' => 'event_end_datetime',
                            'value' => $now,
                            'compare' => '>=',
                            'type' => 'DATETIME'
                        )
                    );
                    break;
                case 'past':
                    $meta_query[] = array(
                        'key' => 'event_end_datetime',
                        'value' => $now,
                        'compare' => '<',
                        'type' => 'DATETIME'
                    );
                    break;
            }

            if (!empty($meta_query)) {
                $query->set('meta_query', $meta_query);
            }
        }
    }
}
add_action('pre_get_posts', 'inthub_event_archive_query');

/**
 * Add Event Structured Data (Schema.org)
 */
function inthub_event_structured_data() {
    if (is_singular('event')) {
        global $post;

        $start_datetime = get_post_meta($post->ID, 'event_start_datetime', true);
        $end_datetime = get_post_meta($post->ID, 'event_end_datetime', true);
        $location = get_post_meta($post->ID, 'event_location', true);
        $short_description = get_post_meta($post->ID, 'event_short_description', true);

        if ($start_datetime && $end_datetime && $location) {
            $schema = array(
                '@context' => 'https://schema.org',
                '@type' => 'Event',
                'name' => get_the_title(),
                'description' => $short_description ?: get_the_excerpt(),
                'startDate' => date('c', strtotime($start_datetime)),
                'endDate' => date('c', strtotime($end_datetime)),
                'location' => array(
                    '@type' => 'Place',
                    'name' => $location,
                    'address' => $location
                ),
                'organizer' => array(
                    '@type' => 'Organization',
                    'name' => get_bloginfo('name'),
                    'url' => home_url()
                )
            );

            if (has_post_thumbnail()) {
                $schema['image'] = get_the_post_thumbnail_url($post->ID, 'large');
            }

            echo '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES) . '</script>' . "\n";
        }
    }
}
add_action('wp_head', 'inthub_event_structured_data');

/**
 * Customizer Settings
 */
function inthub_customize_register($wp_customize) {
    // Site Identity Section
    $wp_customize->add_setting('inthub_site_description', array(
        'default' => __('Your study abroad partner', 'inthub'),
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('inthub_site_description', array(
        'label' => __('Site Description', 'inthub'),
        'section' => 'title_tagline',
        'type' => 'text',
    ));
    
    // Contact Information Section
    $wp_customize->add_section('inthub_contact', array(
        'title' => __('Contact Information', 'inthub'),
        'priority' => 30,
    ));
    
    // Phone Number
    $wp_customize->add_setting('inthub_phone', array(
        'default' => '+84 28 1234 5678',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    
    $wp_customize->add_control('inthub_phone', array(
        'label' => __('Phone Number', 'inthub'),
        'section' => 'inthub_contact',
        'type' => 'text',
    ));
    
    // Email Address
    $wp_customize->add_setting('inthub_email', array(
        'default' => '<EMAIL>',
        'sanitize_callback' => 'sanitize_email',
    ));
    
    $wp_customize->add_control('inthub_email', array(
        'label' => __('Email Address', 'inthub'),
        'section' => 'inthub_contact',
        'type' => 'email',
    ));
    
    // Address
    $wp_customize->add_setting('inthub_address', array(
        'default' => '123 Nguyễn Huệ, Quận 1, TP. Hồ Chí Minh',
        'sanitize_callback' => 'sanitize_textarea_field',
    ));
    
    $wp_customize->add_control('inthub_address', array(
        'label' => __('Address', 'inthub'),
        'section' => 'inthub_contact',
        'type' => 'textarea',
    ));
    
    // Social Media Section
    $wp_customize->add_section('inthub_social', array(
        'title' => __('Social Media', 'inthub'),
        'priority' => 35,
    ));
    
    // Facebook URL
    $wp_customize->add_setting('inthub_facebook', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('inthub_facebook', array(
        'label' => __('Facebook URL', 'inthub'),
        'section' => 'inthub_social',
        'type' => 'url',
    ));
    
    // Twitter URL
    $wp_customize->add_setting('inthub_twitter', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('inthub_twitter', array(
        'label' => __('Twitter URL', 'inthub'),
        'section' => 'inthub_social',
        'type' => 'url',
    ));
    
    // Instagram URL
    $wp_customize->add_setting('inthub_instagram', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('inthub_instagram', array(
        'label' => __('Instagram URL', 'inthub'),
        'section' => 'inthub_social',
        'type' => 'url',
    ));
    
    // LinkedIn URL
    $wp_customize->add_setting('inthub_linkedin', array(
        'default' => '',
        'sanitize_callback' => 'esc_url_raw',
    ));
    
    $wp_customize->add_control('inthub_linkedin', array(
        'label' => __('LinkedIn URL', 'inthub'),
        'section' => 'inthub_social',
        'type' => 'url',
    ));
}
add_action('customize_register', 'inthub_customize_register');

/**
 * Custom Excerpt Length
 */
function inthub_excerpt_length($length) {
    return 20;
}
add_filter('excerpt_length', 'inthub_excerpt_length');

/**
 * Custom Excerpt More
 */
function inthub_excerpt_more($more) {
    return '...';
}
add_filter('excerpt_more', 'inthub_excerpt_more');

/**
 * Add Custom Body Classes
 */
function inthub_body_classes($classes) {
    if (is_front_page()) {
        $classes[] = 'home-page';
    }
    
    if (is_page()) {
        $classes[] = 'page-' . get_post_field('post_name');
    }
    
    return $classes;
}
add_filter('body_class', 'inthub_body_classes');

/**
 * Custom Walker for Navigation Menu
 */
class IntHub_Walker_Nav_Menu extends Walker_Nav_Menu {
    function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
        $classes = empty($item->classes) ? array() : (array) $item->classes;
        $classes[] = 'menu-item-' . $item->ID;
        
        $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
        $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';
        
        $id = apply_filters('nav_menu_item_id', 'menu-item-' . $item->ID, $item, $args);
        $id = $id ? ' id="' . esc_attr($id) . '"' : '';
        
        $output .= '<li' . $id . $class_names . '>';
        
        $attributes = !empty($item->attr_title) ? ' title="' . esc_attr($item->attr_title) . '"' : '';
        $attributes .= !empty($item->target) ? ' target="' . esc_attr($item->target) . '"' : '';
        $attributes .= !empty($item->xfn) ? ' rel="' . esc_attr($item->xfn) . '"' : '';
        $attributes .= !empty($item->url) ? ' href="' . esc_attr($item->url) . '"' : '';
        
        $item_output = isset($args->before) ? $args->before : '';
        $item_output .= '<a' . $attributes . ' class="nav-link text-blue-900 font-medium hover:text-pink-500 transition duration-300">';
        $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
        $item_output .= '</a>';
        $item_output .= isset($args->after) ? $args->after : '';
        
        $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
    }
}
