<?php
/**
 * Single template for University post type
 *
 * @package IntHub
 * @since 1.0.0
 */

get_header();

while (have_posts()) : the_post();
    // Get custom fields
    $university_name = get_post_meta(get_the_ID(), 'university_name', true);
    $establishment_year = get_post_meta(get_the_ID(), 'establishment_year', true);
    $location = get_post_meta(get_the_ID(), 'location', true);
    $overview = get_post_meta(get_the_ID(), 'overview', true);
    $website_url = get_post_meta(get_the_ID(), 'website_url', true);
    $courses = get_post_meta(get_the_ID(), 'courses', true);
    $country = get_post_meta(get_the_ID(), 'country', true);
    $tuition_fee = get_post_meta(get_the_ID(), 'tuition_fee', true);
    $admission_info = get_post_meta(get_the_ID(), 'admission_info', true);
    $map = get_post_meta(get_the_ID(), 'map', true);
    $video_url = get_post_meta(get_the_ID(), 'video_url', true);
    
    // Get country name
    $country_names = array(
        'usa' => 'Hoa Kỳ',
        'uk' => 'Anh',
        'canada' => 'Canada',
        'australia' => 'Úc',
        'germany' => 'Đức',
        'france' => 'Pháp',
        'japan' => 'Nhật Bản',
        'south_korea' => 'Hàn Quốc',
        'singapore' => 'Singapore',
        'netherlands' => 'Hà Lan',
        'other' => 'Khác'
    );
    $country_display = isset($country_names[$country]) ? $country_names[$country] : $country;
    
    // Get related scholarships
    $scholarships = get_posts(array(
        'post_type' => 'scholarship',
        'meta_query' => array(
            array(
                'key' => 'related_university',
                'value' => get_the_ID(),
                'compare' => '='
            )
        ),
        'posts_per_page' => -1,
        'post_status' => 'publish'
    ));
?>

<div class="min-h-screen bg-gray-50">
    <!-- Hero Section -->
    <section class="relative" style="background-image: url('<?php echo get_the_post_thumbnail_url(get_the_ID(), 'full'); ?>'); background-size: cover; background-position: center;">
        <?php if (has_post_thumbnail()) : ?>
            <div class="relative h-96 overflow-hidden">
                <?php the_post_thumbnail('full', array('class' => 'w-full h-full object-cover')); ?>
                <div class="absolute inset-0 bg-gradient-to-r from-[#004aad]/80 to-[#fa3c80]/80"></div>
            </div>
        <?php else : ?>
            <div class="h-96 bg-gradient-to-r from-[#004aad] to-[#fa3c80]"></div>
        <?php endif; ?>
        
        <div class="absolute inset-0 flex items-center">
            <div class="container mx-auto px-4">
                <div class="text-white max-w-4xl">
                    <h1 class="text-4xl md:text-5xl font-bold mb-4">
                        <?php echo esc_html($university_name ?: get_the_title()); ?>
                    </h1>
                    <?php if ($location) : ?>
                        <div class="flex items-center text-xl opacity-90 mb-6">
                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span><?php echo esc_html($location); ?></span>
                        </div>
                    <?php endif; ?>
                    
                    <div class="flex flex-wrap gap-4">
                        <?php if ($website_url) : ?>
                            <a href="<?php echo esc_url($website_url); ?>" target="_blank" 
                               class="inline-flex items-center bg-white text-[#004aad] px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                </svg>
                                <?php _e('Website chính thức', 'inthub'); ?>
                            </a>
                        <?php endif; ?>
                        
                        <?php if (!empty($scholarships)) : ?>
                        <a href="#scholarships"
                           class="inline-flex items-center bg-[#fa3c80] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#fa3c80]/90 transition-colors">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <?php _e('Xem học bổng', 'inthub'); ?> (<?php echo count($scholarships); ?>)
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content -->
    <section class="py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <!-- Main Content -->
                <div class="lg:col-span-2">
                    <!-- University Tabs -->
                    <div class="university-tabs">
                        <!-- Tab Navigation -->
                        <nav class="university-tabs-nav">
                            <button class="university-tab-button active" data-tab="overview">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <?php _e('Tổng quan', 'inthub'); ?>
                            </button>
                            <?php if ($courses) : ?>
                            <button class="university-tab-button" data-tab="courses">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <?php _e('Khóa học', 'inthub'); ?>
                            </button>
                            <?php endif; ?>
                            <?php if ($admission_info) : ?>
                            <button class="university-tab-button" data-tab="admission">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                </svg>
                                <?php _e('Thông tin tuyển sinh', 'inthub'); ?>
                            </button>
                            <?php endif; ?>
                            <?php if (!empty($scholarships)) : ?>
                            <button class="university-tab-button" data-tab="scholarships">
                                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                <?php _e('Học bổng', 'inthub'); ?> (<?php echo count($scholarships); ?>)
                            </button>
                            <?php endif; ?>
                        </nav>

                        <!-- Tab Content -->
                        <div class="university-tab-content">
                            <!-- Overview Tab -->
                            <div id="overview" class="university-tab-pane active">
                                <?php if ($overview || get_the_content()) : ?>
                                    <div class="tab-section">
                                        <div class="prose prose-lg max-w-none">
                                            <?php if ($overview) : ?>
                                                <?php echo wp_kses_post($overview); ?>
                                            <?php else : ?>
                                                <?php the_content(); ?>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Video in Overview -->
                                <?php if ($video_url) : ?>
                                    <div class="tab-section">
                                        <h3 class="tab-section-title">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                            </svg>
                                            <?php _e('Video giới thiệu', 'inthub'); ?>
                                        </h3>
                                        <div class="aspect-w-16 aspect-h-9">
                                            <?php
                                            // Convert YouTube URL to embed format
                                            $embed_url = $video_url;
                                            if (strpos($video_url, 'youtube.com/watch') !== false) {
                                                $video_id = parse_url($video_url, PHP_URL_QUERY);
                                                parse_str($video_id, $params);
                                                if (isset($params['v'])) {
                                                    $embed_url = 'https://www.youtube.com/embed/' . $params['v'];
                                                }
                                            } elseif (strpos($video_url, 'youtu.be/') !== false) {
                                                $video_id = basename(parse_url($video_url, PHP_URL_PATH));
                                                $embed_url = 'https://www.youtube.com/embed/' . $video_id;
                                            }
                                            ?>
                                            <iframe src="<?php echo esc_url($embed_url); ?>"
                                                    class="w-full h-64 md:h-96 rounded-lg"
                                                    frameborder="0"
                                                    allowfullscreen></iframe>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <!-- Map in Overview -->
                                <?php if ($map) : ?>
                                    <div class="tab-section">
                                        <h3 class="tab-section-title">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            </svg>
                                            <?php _e('Vị trí', 'inthub'); ?>
                                        </h3>
                                        <div class="rounded-lg overflow-hidden">
                                            <?php echo wp_kses_post($map); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Courses Tab -->
                            <?php if ($courses) : ?>
                            <div id="courses" class="university-tab-pane">
                                <div class="tab-section">
                                    <h3 class="tab-section-title">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                        <?php _e('Danh sách khóa học', 'inthub'); ?>
                                    </h3>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <?php
                                        $courses_list = explode("\n", $courses);
                                        foreach ($courses_list as $course) :
                                            $course = trim($course);
                                            if (!empty($course)) :
                                        ?>
                                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                                <svg class="w-5 h-5 text-[#fa3c80] mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-gray-700"><?php echo esc_html($course); ?></span>
                                            </div>
                                        <?php
                                            endif;
                                        endforeach;
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Admission Info Tab -->
                            <?php if ($admission_info) : ?>
                            <div id="admission" class="university-tab-pane">
                                <div class="tab-section">
                                    <h3 class="tab-section-title">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                                        </svg>
                                        <?php _e('Thời gian tuyển sinh', 'inthub'); ?>
                                    </h3>
                                    <div class="prose prose-lg max-w-none">
                                        <?php echo wp_kses_post($admission_info); ?>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>

                            <!-- Scholarships Tab -->
                            <?php if (!empty($scholarships)) : ?>
                            <div id="scholarships" class="university-tab-pane">
                                <div class="tab-section">
                                    <h3 class="tab-section-title">
                                        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                        </svg>
                                        <?php _e('Học bổng có sẵn', 'inthub'); ?>
                                    </h3>
                                    <p class="text-gray-600 mb-6">
                                        <?php printf(__('Khám phá %d chương trình học bổng tại %s', 'inthub'), count($scholarships), esc_html($university_name ?: get_the_title())); ?>
                                    </p>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <?php foreach ($scholarships as $scholarship) : ?>
                                            <?php
                                            $scholarship_name = get_post_meta($scholarship->ID, 'scholarship_name', true);
                                            $scholarship_value = get_post_meta($scholarship->ID, 'scholarship_value', true);
                                            $tuition_support = get_post_meta($scholarship->ID, 'tuition_support', true);
                                            ?>

                                            <div class="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#fa3c80]">
                                                <div class="flex items-start justify-between mb-4">
                                                    <div class="w-12 h-12 bg-[#fa3c80] bg-opacity-10 rounded-full flex items-center justify-center">
                                                        <svg class="w-6 h-6 text-[#fa3c80]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                                        </svg>
                                                    </div>
                                                    <span class="bg-[#004aad] text-white px-3 py-1 rounded-full text-xs font-medium">
                                                        <?php _e('Học bổng', 'inthub'); ?>
                                                    </span>
                                                </div>

                                                <h4 class="text-xl font-bold text-[#004aad] mb-3">
                                                    <a href="<?php echo get_permalink($scholarship->ID); ?>" class="hover:text-[#fa3c80] transition-colors">
                                                        <?php echo esc_html($scholarship_name ?: $scholarship->post_title); ?>
                                                    </a>
                                                </h4>

                                                <?php if ($scholarship_value) : ?>
                                                    <div class="flex items-center text-[#fa3c80] font-semibold mb-2">
                                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                                        </svg>
                                                        <span><?php echo esc_html($scholarship_value); ?></span>
                                                    </div>
                                                <?php endif; ?>

                                                <?php if ($tuition_support) : ?>
                                                    <p class="text-gray-600 text-sm mb-4">
                                                        <strong><?php _e('Hỗ trợ:', 'inthub'); ?></strong> <?php echo esc_html($tuition_support); ?>
                                                    </p>
                                                <?php endif; ?>

                                                <?php if ($scholarship->post_excerpt) : ?>
                                                    <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                                        <?php echo wp_trim_words($scholarship->post_excerpt, 15); ?>
                                                    </p>
                                                <?php endif; ?>

                                                <a href="<?php echo get_permalink($scholarship->ID); ?>"
                                                   class="inline-flex items-center text-[#004aad] hover:text-[#fa3c80] font-medium text-sm transition-colors">
                                                    <?php _e('Xem chi tiết', 'inthub'); ?>
                                                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                                    </svg>
                                                </a>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <div class="text-center mt-8">
                                        <a href="<?php echo get_post_type_archive_link('scholarship'); ?>?university=<?php echo get_the_ID(); ?>"
                                           class="inline-flex items-center bg-[#fa3c80] text-white px-8 py-3 rounded-lg font-medium hover:bg-[#fa3c80]/90 transition-colors">
                                            <?php _e('Xem tất cả học bổng', 'inthub'); ?>
                                            <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1">
                    <!-- Quick Info -->
                    <div class="bg-white rounded-xl shadow-lg p-6 mb-8 sticky top-8">
                        <h3 class="text-xl font-bold text-[#004aad] mb-4">
                            <?php _e('Thông tin nhanh', 'inthub'); ?>
                        </h3>
                        
                        <div class="space-y-4">
                            <?php if ($country_display) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Quốc gia', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($country_display); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($establishment_year) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Năm thành lập', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($establishment_year); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($tuition_fee) : ?>
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                    <div>
                                        <div class="text-sm text-gray-500"><?php _e('Học phí', 'inthub'); ?></div>
                                        <div class="font-medium"><?php echo esc_html($tuition_fee); ?></div>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-[#fa3c80] mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                                <div>
                                    <div class="text-sm text-gray-500"><?php _e('Học bổng có sẵn', 'inthub'); ?></div>
                                    <div class="font-medium"><?php echo count($scholarships); ?> <?php _e('chương trình', 'inthub'); ?></div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($website_url) : ?>
                            <div class="mt-6 pt-6 border-t">
                                <a href="<?php echo esc_url($website_url); ?>" target="_blank" 
                                   class="w-full inline-flex items-center justify-center bg-[#004aad] text-white px-4 py-3 rounded-lg font-medium hover:bg-[#004aad]/90 transition-colors">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                    <?php _e('Truy cập website', 'inthub'); ?>
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>


</div>

<?php endwhile; ?>

<?php get_footer(); ?>
